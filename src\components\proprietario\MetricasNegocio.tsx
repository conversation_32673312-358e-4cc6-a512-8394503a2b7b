'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface MetricasNegocioProps {
  className?: string;
}

export function MetricasNegocio({ className = '' }: MetricasNegocioProps) {
  const { metricas, loading, error } = useEmpresaProprietario();

  if (loading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {[1, 2, 3, 4].map(i => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-20"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
                <div className="h-3 bg-gray-200 rounded w-24"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !metricas) {
    return (
      <Card className={`border-red-200 bg-red-50 ${className}`}>
        <CardContent className="p-6">
          <div className="text-center">
            <div className="text-red-600 text-2xl mb-2">📊</div>
            <div className="text-red-800 font-semibold">Erro ao carregar métricas</div>
            <div className="text-red-700 text-sm mt-1">
              {error || 'Não foi possível carregar as métricas do negócio'}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatarMoeda = (valor: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(valor);
  };

  const formatarPercentual = (valor: number) => {
    return `${valor.toFixed(1)}%`;
  };

  const metricsCards = [
    {
      title: 'Receita do Mês',
      value: formatarMoeda(metricas.receita_bruta_mes),
      subtitle: `Líquida: ${formatarMoeda(metricas.receita_liquida_mes)}`,
      icon: '💰',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      title: 'Agendamentos',
      value: metricas.total_agendamentos_mes.toString(),
      subtitle: `${metricas.agendamentos_pendentes} pendentes`,
      icon: '📅',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      title: 'Clientes Ativos',
      value: metricas.total_clientes_ativos.toString(),
      subtitle: 'Este mês',
      icon: '👥',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    {
      title: 'Taxa Confirmação',
      value: formatarPercentual(metricas.taxa_confirmacao_mes),
      subtitle: 'Agendamentos confirmados',
      icon: '✅',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  ];

  const statusCards = [
    {
      title: 'Serviços Ativos',
      value: metricas.total_servicos_ativos.toString(),
      icon: '🛠️',
      color: 'text-indigo-600'
    },
    {
      title: 'Colaboradores',
      value: metricas.total_colaboradores_ativos.toString(),
      icon: '👨‍💼',
      color: 'text-teal-600'
    }
  ];

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Métricas principais */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
          📊 Métricas do Negócio
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {metricsCards.map((metric, index) => (
            <Card key={index} className={`${metric.bgColor} ${metric.borderColor} border-2`}>
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-medium text-[var(--text-secondary)] mb-1">
                      {metric.title}
                    </div>
                    <div className={`text-2xl font-bold ${metric.color} mb-1`}>
                      {metric.value}
                    </div>
                    <div className="text-xs text-[var(--text-secondary)]">
                      {metric.subtitle}
                    </div>
                  </div>
                  <div className="text-2xl ml-2">
                    {metric.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Status operacional */}
      <div>
        <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-4">
          ⚙️ Status Operacional
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {statusCards.map((status, index) => (
            <Card key={index}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm font-medium text-[var(--text-secondary)]">
                      {status.title}
                    </div>
                    <div className={`text-xl font-bold ${status.color}`}>
                      {status.value}
                    </div>
                  </div>
                  <div className="text-2xl">
                    {status.icon}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Resumo rápido */}
      {metricas.total_agendamentos_mes > 0 && (
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-0">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Valor Médio por Agendamento
                </div>
                <div className="text-lg font-bold text-blue-600">
                  {formatarMoeda(metricas.receita_bruta_mes / metricas.total_agendamentos_mes)}
                </div>
              </div>
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Agendamentos por Cliente
                </div>
                <div className="text-lg font-bold text-purple-600">
                  {metricas.total_clientes_ativos > 0 
                    ? (metricas.total_agendamentos_mes / metricas.total_clientes_ativos).toFixed(1)
                    : '0'
                  }
                </div>
              </div>
              <div>
                <div className="text-sm text-[var(--text-secondary)]">
                  Receita por Colaborador
                </div>
                <div className="text-lg font-bold text-green-600">
                  {metricas.total_colaboradores_ativos > 0
                    ? formatarMoeda(metricas.receita_bruta_mes / metricas.total_colaboradores_ativos)
                    : formatarMoeda(0)
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mensagem quando não há dados */}
      {metricas.total_agendamentos_mes === 0 && (
        <Card className="border-gray-200 bg-gray-50">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="text-gray-400 text-4xl mb-3">📈</div>
              <h3 className="font-semibold text-gray-700 text-lg">Nenhum agendamento este mês</h3>
              <p className="text-gray-600 text-sm mt-2">
                Suas métricas aparecerão aqui assim que você receber os primeiros agendamentos.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
