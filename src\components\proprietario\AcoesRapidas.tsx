'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface AcaoRapida {
  id: string;
  titulo: string;
  descricao: string;
  icone: string;
  href: string;
  prioridade: 'alta' | 'media' | 'baixa';
  categoria: 'essencial' | 'gestao' | 'premium' | 'configuracao';
  disponivel: boolean;
  badge?: string;
}

interface AcoesRapidasProps {
  className?: string;
}

export function AcoesRapidas({ className = '' }: AcoesRapidasProps) {
  const { 
    empresa, 
    planoSaas, 
    metricas,
    precisaConfiguracaoInicial,
    podeReceberPagamentos
  } = useEmpresaProprietario();

  const isPremium = planoSaas?.recursos_premium || false;
  const temEmpresa = !!empresa;
  const empresaAtiva = empresa?.status === 'ativo';

  const acoes: AcaoRapida[] = [
    // Ações essenciais
    {
      id: 'agenda',
      titulo: 'Agenda',
      descricao: 'Visualizar e gerenciar agendamentos',
      icone: '📅',
      href: '/proprietario/agendamentos',
      prioridade: 'alta',
      categoria: 'essencial',
      disponivel: temEmpresa && empresaAtiva,
      badge: metricas?.agendamentos_pendentes ? `${metricas.agendamentos_pendentes} pendentes` : undefined
    },
    {
      id: 'servicos',
      titulo: 'Serviços',
      descricao: 'Gerenciar serviços oferecidos',
      icone: '🛠️',
      href: '/proprietario/servicos',
      prioridade: 'alta',
      categoria: 'essencial',
      disponivel: temEmpresa,
      badge: metricas?.total_servicos_ativos === 0 ? 'Configurar' : undefined
    },
    {
      id: 'colaboradores',
      titulo: 'Colaboradores',
      descricao: 'Convidar e gerenciar equipe',
      icone: '👥',
      href: '/proprietario/colaboradores',
      prioridade: 'alta',
      categoria: 'gestao',
      disponivel: temEmpresa,
      badge: metricas?.total_colaboradores_ativos === 0 ? 'Adicionar' : undefined
    },
    {
      id: 'horarios',
      titulo: 'Horários',
      descricao: 'Configurar horários de funcionamento',
      icone: '⏰',
      href: '/proprietario/horarios',
      prioridade: 'media',
      categoria: 'configuracao',
      disponivel: temEmpresa
    },

    // Ações de gestão
    {
      id: 'relatorios',
      titulo: 'Relatórios',
      descricao: 'Visualizar estatísticas e relatórios',
      icone: '📊',
      href: '/proprietario/relatorios',
      prioridade: 'media',
      categoria: 'gestao',
      disponivel: temEmpresa && empresaAtiva
    },
    {
      id: 'configuracoes',
      titulo: 'Configurações',
      descricao: 'Configurar empresa e preferências',
      icone: '⚙️',
      href: '/proprietario/configuracoes',
      prioridade: 'media',
      categoria: 'configuracao',
      disponivel: temEmpresa,
      badge: precisaConfiguracaoInicial() ? 'Pendente' : undefined
    },

    // Ações premium
    {
      id: 'marketing',
      titulo: 'Marketing',
      descricao: 'Campanhas e cupons de desconto',
      icone: '📢',
      href: '/proprietario/marketing',
      prioridade: 'baixa',
      categoria: 'premium',
      disponivel: isPremium && temEmpresa && empresaAtiva,
      badge: 'Premium'
    },
    {
      id: 'planos-assinatura',
      titulo: 'Planos Cliente',
      descricao: 'Criar planos de assinatura',
      icone: '💎',
      href: '/proprietario/planos-assinatura',
      prioridade: 'baixa',
      categoria: 'premium',
      disponivel: isPremium && temEmpresa && empresaAtiva,
      badge: 'Premium'
    },

    // Ações de pagamento
    {
      id: 'pagamentos',
      titulo: 'Pagamentos',
      descricao: 'Configurar Stripe Connect',
      icone: '💳',
      href: '/proprietario/configuracoes?tab=pagamentos',
      prioridade: 'alta',
      categoria: 'configuracao',
      disponivel: temEmpresa,
      badge: !podeReceberPagamentos() ? 'Configurar' : undefined
    }
  ];

  const getCardStyle = (acao: AcaoRapida) => {
    if (!acao.disponivel) {
      return {
        card: 'opacity-50 cursor-not-allowed bg-gray-50 border-gray-200',
        content: 'text-gray-500'
      };
    }

    switch (acao.prioridade) {
      case 'alta':
        return {
          card: 'hover:shadow-lg transition-all duration-200 border-blue-200 hover:border-blue-300',
          content: 'text-[var(--text-primary)]'
        };
      case 'media':
        return {
          card: 'hover:shadow-md transition-all duration-200 hover:border-[var(--primary)]',
          content: 'text-[var(--text-primary)]'
        };
      case 'baixa':
        return {
          card: 'hover:shadow-sm transition-all duration-200',
          content: 'text-[var(--text-secondary)]'
        };
      default:
        return {
          card: '',
          content: 'text-[var(--text-primary)]'
        };
    }
  };

  const getBadgeStyle = (categoria: string) => {
    switch (categoria) {
      case 'premium':
        return 'bg-purple-100 text-purple-800';
      case 'essencial':
        return 'bg-blue-100 text-blue-800';
      case 'configuracao':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Filtrar e ordenar ações
  const acoesDisponiveis = acoes.filter(acao => acao.disponivel);
  const acoesIndisponiveis = acoes.filter(acao => !acao.disponivel);
  
  const acoesOrdenadas = [
    ...acoesDisponiveis.sort((a, b) => {
      const prioridadeOrder = { alta: 3, media: 2, baixa: 1 };
      return prioridadeOrder[b.prioridade] - prioridadeOrder[a.prioridade];
    }),
    ...acoesIndisponiveis
  ];

  return (
    <div className={className}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
          🚀 Ações Rápidas
        </h3>
        <p className="text-sm text-[var(--text-secondary)]">
          Acesse rapidamente as principais funcionalidades do seu negócio
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {acoesOrdenadas.map((acao) => {
          const style = getCardStyle(acao);
          const CardComponent = acao.disponivel ? Link : 'div';
          
          return (
            <CardComponent
              key={acao.id}
              href={acao.disponivel ? acao.href : '#'}
              className={acao.disponivel ? '' : 'cursor-not-allowed'}
            >
              <Card className={style.card}>
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="text-2xl">{acao.icone}</div>
                    {acao.badge && (
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${getBadgeStyle(acao.categoria)}`}>
                        {acao.badge}
                      </span>
                    )}
                  </div>
                  
                  <div>
                    <h4 className={`font-semibold mb-1 ${style.content}`}>
                      {acao.titulo}
                    </h4>
                    <p className={`text-sm ${style.content}`}>
                      {acao.descricao}
                    </p>
                  </div>

                  {acao.disponivel && (
                    <div className="mt-3">
                      <Button 
                        size="sm" 
                        variant="outline" 
                        className="w-full text-xs"
                      >
                        Acessar
                      </Button>
                    </div>
                  )}

                  {!acao.disponivel && (
                    <div className="mt-3">
                      <div className="text-xs text-gray-500 text-center py-2">
                        {acao.categoria === 'premium' ? 'Requer plano Premium' : 'Não disponível'}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </CardComponent>
          );
        })}
      </div>

      {/* Upgrade para Premium */}
      {!isPremium && (
        <Card className="mt-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="text-2xl">🌟</div>
                <div>
                  <div className="font-semibold text-purple-800">
                    Desbloqueie recursos Premium
                  </div>
                  <div className="text-sm text-purple-700">
                    Acesse campanhas de marketing, planos de assinatura e muito mais
                  </div>
                </div>
              </div>
              <Link href="/planos">
                <Button className="bg-purple-600 hover:bg-purple-700">
                  Upgrade
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
