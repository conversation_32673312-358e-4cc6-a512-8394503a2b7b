import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { StripeConnectUtils } from '@/utils/stripe/connect';

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado. Apenas proprietários podem acessar configurações de pagamento.' },
        { status: 403 }
      );
    }

    // Buscar dados da empresa do proprietário
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .select(`
        empresa_id,
        nome_empresa,
        stripe_account_id,
        stripe_account_status,
        stripe_charges_enabled,
        stripe_payouts_enabled,
        pagamentos_online_habilitados,
        percentual_comissao_plataforma
      `)
      .eq('proprietario_user_id', user.id)
      .single();

    if (empresaError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Empresa não encontrada' },
        { status: 404 }
      );
    }

    // Se não tem conta Stripe conectada
    if (!empresa.stripe_account_id) {
      return NextResponse.json({
        success: true,
        data: {
          connected: false,
          status: {
            status: 'not_connected',
            mensagem: 'Conta não conectada',
            cor: 'gray'
          },
          empresa: {
            nome: empresa.nome_empresa,
            pagamentos_habilitados: empresa.pagamentos_online_habilitados,
            percentual_comissao: empresa.percentual_comissao_plataforma
          },
          pode_receber_pagamentos: false,
          requer_configuracao: true
        }
      });
    }

    // Verificar status atual no Stripe
    try {
      const statusConta = await StripeConnectUtils.verificarStatusConta(empresa.stripe_account_id);
      const statusLegivel = StripeConnectUtils.obterStatusLegivel(statusConta);
      const podeReceberPagamentos = StripeConnectUtils.podeReceberPagamentos(statusConta);

      // Atualizar status na base de dados se mudou
      if (
        empresa.stripe_account_status !== statusLegivel.status ||
        empresa.stripe_charges_enabled !== statusConta.charges_enabled ||
        empresa.stripe_payouts_enabled !== statusConta.payouts_enabled
      ) {
        const { error: updateError } = await supabase
          .from('empresas')
          .update({
            stripe_account_status: statusLegivel.status,
            stripe_charges_enabled: statusConta.charges_enabled,
            stripe_payouts_enabled: statusConta.payouts_enabled,
            updated_at: new Date().toISOString()
          })
          .eq('empresa_id', empresa.empresa_id);

        if (updateError) {
          console.error('Erro ao atualizar status da conta:', updateError);
        }
      }

      // Obter URL do dashboard se a conta estiver ativa
      let dashboardUrl = null;
      if (podeReceberPagamentos) {
        try {
          dashboardUrl = await StripeConnectUtils.gerarUrlDashboard(empresa.stripe_account_id);
        } catch (error) {
          console.error('Erro ao gerar URL do dashboard:', error);
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          connected: true,
          account_id: empresa.stripe_account_id,
          status: statusLegivel,
          charges_enabled: statusConta.charges_enabled,
          payouts_enabled: statusConta.payouts_enabled,
          details_submitted: statusConta.details_submitted,
          requirements: statusConta.requirements,
          capabilities: statusConta.capabilities,
          empresa: {
            nome: empresa.nome_empresa,
            pagamentos_habilitados: empresa.pagamentos_online_habilitados,
            percentual_comissao: empresa.percentual_comissao_plataforma
          },
          pode_receber_pagamentos: podeReceberPagamentos,
          requer_configuracao: !statusConta.details_submitted,
          dashboard_url: dashboardUrl
        }
      });

    } catch (stripeError: any) {
      console.error('Erro ao verificar conta no Stripe:', stripeError);

      // Se a conta não existe mais no Stripe, limpar da base de dados
      if (stripeError.code === 'resource_missing') {
        const { error: clearError } = await supabase
          .from('empresas')
          .update({
            stripe_account_id: null,
            stripe_account_status: 'not_connected',
            stripe_charges_enabled: false,
            stripe_payouts_enabled: false,
            updated_at: new Date().toISOString()
          })
          .eq('empresa_id', empresa.empresa_id);

        if (clearError) {
          console.error('Erro ao limpar conta Stripe inválida:', clearError);
        }

        return NextResponse.json({
          success: true,
          data: {
            connected: false,
            status: {
              status: 'not_connected',
              mensagem: 'Conta não encontrada',
              cor: 'gray'
            },
            empresa: {
              nome: empresa.nome_empresa,
              pagamentos_habilitados: empresa.pagamentos_online_habilitados,
              percentual_comissao: empresa.percentual_comissao_plataforma
            },
            pode_receber_pagamentos: false,
            requer_configuracao: true
          }
        });
      }

      // Para outros erros, retornar status baseado nos dados locais
      return NextResponse.json({
        success: true,
        data: {
          connected: true,
          account_id: empresa.stripe_account_id,
          status: {
            status: empresa.stripe_account_status || 'pending',
            mensagem: 'Erro ao verificar status',
            cor: 'yellow'
          },
          charges_enabled: empresa.stripe_charges_enabled,
          payouts_enabled: empresa.stripe_payouts_enabled,
          empresa: {
            nome: empresa.nome_empresa,
            pagamentos_habilitados: empresa.pagamentos_online_habilitados,
            percentual_comissao: empresa.percentual_comissao_plataforma
          },
          pode_receber_pagamentos: false,
          requer_configuracao: true,
          erro_verificacao: true
        }
      });
    }

  } catch (error: any) {
    console.error('Erro ao verificar status do Stripe Connect:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Verificar autenticação
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Usuário não autenticado' },
        { status: 401 }
      );
    }

    // Verificar se é proprietário
    if (user.user_metadata?.role !== 'Proprietario') {
      return NextResponse.json(
        { success: false, error: 'Acesso negado' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { pagamentos_online_habilitados } = body;

    if (typeof pagamentos_online_habilitados !== 'boolean') {
      return NextResponse.json(
        { success: false, error: 'Campo pagamentos_online_habilitados deve ser boolean' },
        { status: 400 }
      );
    }

    // Atualizar configuração na empresa
    const { data: empresa, error: updateError } = await supabase
      .from('empresas')
      .update({
        pagamentos_online_habilitados,
        updated_at: new Date().toISOString()
      })
      .eq('proprietario_user_id', user.id)
      .select('empresa_id, nome_empresa')
      .single();

    if (updateError || !empresa) {
      return NextResponse.json(
        { success: false, error: 'Erro ao atualizar configuração' },
        { status: 500 }
      );
    }

    console.log('✅ Configuração de pagamentos atualizada:', {
      empresa_id: empresa.empresa_id,
      pagamentos_habilitados: pagamentos_online_habilitados,
      proprietario: user.id
    });

    return NextResponse.json({
      success: true,
      data: {
        empresa_id: empresa.empresa_id,
        nome_empresa: empresa.nome_empresa,
        pagamentos_online_habilitados
      }
    });

  } catch (error: any) {
    console.error('Erro ao atualizar configuração de pagamentos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}
