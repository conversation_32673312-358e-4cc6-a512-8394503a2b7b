'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { StripeConnectConfig } from '@/components/stripe/StripeConnectConfig';
import { PoliticasCancelamento } from '@/components/configuracoes/PoliticasCancelamento';
import { useAuth } from '@/contexts/AuthContext';

function ConfiguracoesProprietario() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [mostrarMensagemRetorno, setMostrarMensagemRetorno] = useState(false);
  const [tipoRetorno, setTipoRetorno] = useState<'success' | 'refresh' | null>(null);
  const [abaAtiva, setAbaAtiva] = useState<'pagamentos' | 'cancelamento' | 'geral' | 'notificacoes'>('pagamentos');
  const [empresaId, setEmpresaId] = useState<number | null>(null);

  // Buscar empresa do proprietário
  useEffect(() => {
    async function buscarEmpresa() {
      if (!user) return;

      try {
        const response = await fetch('/api/empresas/proprietario');
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.empresa) {
            setEmpresaId(data.empresa.empresa_id);
          }
        }
      } catch (error) {
        console.error('Erro ao buscar empresa:', error);
      }
    }

    buscarEmpresa();
  }, [user]);

  // Verificar se voltou do Stripe
  useEffect(() => {
    const stripeReturn = searchParams.get('stripe_return');

    if (stripeReturn === 'success') {
      setTipoRetorno('success');
      setMostrarMensagemRetorno(true);

      // Limpar URL
      const url = new URL(window.location.href);
      url.searchParams.delete('stripe_return');
      window.history.replaceState({}, '', url.toString());

      // Esconder mensagem após 5 segundos
      setTimeout(() => {
        setMostrarMensagemRetorno(false);
      }, 5000);

    } else if (stripeReturn === 'refresh') {
      setTipoRetorno('refresh');
      setMostrarMensagemRetorno(true);

      // Limpar URL
      const url = new URL(window.location.href);
      url.searchParams.delete('stripe_return');
      window.history.replaceState({}, '', url.toString());

      // Esconder mensagem após 3 segundos
      setTimeout(() => {
        setMostrarMensagemRetorno(false);
      }, 3000);
    }
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-[var(--background)]">
      {/* Header */}
      <div className="bg-white border-b border-[var(--border)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/proprietario/dashboard')}
              >
                <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                Voltar
              </Button>
              <div>
                <h1 className="text-xl font-semibold text-[var(--text-primary)]">
                  Configurações
                </h1>
                <p className="text-sm text-[var(--text-secondary)]">
                  Gerencie as configurações do seu estabelecimento
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Mensagem de Retorno do Stripe */}
        {mostrarMensagemRetorno && (
          <Card className={`mb-6 ${tipoRetorno === 'success' ? 'bg-[var(--success-light)] border-[var(--success)]' : 'bg-[var(--warning-light)] border-[var(--warning)]'}`}>
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className={`w-6 h-6 rounded-full flex items-center justify-center mt-0.5 ${tipoRetorno === 'success' ? 'bg-[var(--success)]' : 'bg-[var(--warning)]'}`}>
                  {tipoRetorno === 'success' ? (
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-[var(--text-primary)] mb-1">
                    {tipoRetorno === 'success' ? 'Configuração Concluída!' : 'Configuração Pendente'}
                  </h4>
                  <p className="text-sm text-[var(--text-secondary)]">
                    {tipoRetorno === 'success' 
                      ? 'Sua conta Stripe foi configurada com sucesso. Você já pode receber pagamentos online!'
                      : 'A configuração da sua conta Stripe ainda não foi concluída. Complete o processo para receber pagamentos online.'
                    }
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMostrarMensagemRetorno(false)}
                >
                  <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navegação de Configurações */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setAbaAtiva('pagamentos')}
              className={`border-b-2 pb-2 px-1 text-sm font-medium transition-colors ${
                abaAtiva === 'pagamentos'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-[var(--border)]'
              }`}
            >
              Pagamentos
            </button>
            <button
              onClick={() => setAbaAtiva('cancelamento')}
              className={`border-b-2 pb-2 px-1 text-sm font-medium transition-colors ${
                abaAtiva === 'cancelamento'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-[var(--border)]'
              }`}
            >
              Cancelamento
            </button>
            <button
              onClick={() => setAbaAtiva('geral')}
              className={`border-b-2 pb-2 px-1 text-sm font-medium transition-colors ${
                abaAtiva === 'geral'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-[var(--border)]'
              }`}
            >
              Geral
            </button>
            <button
              onClick={() => setAbaAtiva('notificacoes')}
              className={`border-b-2 pb-2 px-1 text-sm font-medium transition-colors ${
                abaAtiva === 'notificacoes'
                  ? 'border-[var(--primary)] text-[var(--primary)]'
                  : 'border-transparent text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:border-[var(--border)]'
              }`}
            >
              Notificações
            </button>
          </nav>
        </div>

        {/* Conteúdo das Abas */}
        {abaAtiva === 'pagamentos' && (
          <>
            <StripeConnectConfig />

            {/* Informações sobre Pagamento Online */}
            <Card className="mt-8 bg-[var(--info-light)] border-[var(--info)]">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className="w-8 h-8 bg-[var(--info)] rounded-full flex items-center justify-center mt-1">
                    <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-[var(--text-primary)] mb-2">
                      Como Funciona o Pagamento Online
                    </h3>
                    <div className="space-y-3 text-sm text-[var(--text-secondary)]">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-[var(--primary)] text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">1</div>
                        <div>
                          <strong>Cliente agenda serviço:</strong> Escolhe "Pagamento Online" durante o agendamento
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-[var(--primary)] text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">2</div>
                        <div>
                          <strong>Pagamento seguro:</strong> Cliente paga com cartão ou Pix através do Stripe
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-[var(--primary)] text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">3</div>
                        <div>
                          <strong>Você recebe:</strong> Valor é transferido para sua conta automaticamente (menos comissões)
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-[var(--primary)] text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">4</div>
                        <div>
                          <strong>Agendamento confirmado:</strong> Cliente e você recebem confirmação por email
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 p-3 bg-white/50 rounded-lg">
                      <h4 className="font-medium text-[var(--text-primary)] mb-2">Benefícios:</h4>
                      <ul className="text-sm text-[var(--text-secondary)] space-y-1">
                        <li>• Recebimento garantido antes do atendimento</li>
                        <li>• Redução de no-shows (faltas)</li>
                        <li>• Processo automatizado e seguro</li>
                        <li>• Histórico completo de transações</li>
                        <li>• Reembolsos automáticos em cancelamentos</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}

        {abaAtiva === 'cancelamento' && empresaId && (
          <PoliticasCancelamento empresaId={empresaId} />
        )}

        {abaAtiva === 'geral' && (
          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-[var(--info-light)] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-[var(--info)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
                  Configurações Gerais
                </h3>
                <p className="text-[var(--text-secondary)]">
                  Esta seção estará disponível em breve
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {abaAtiva === 'notificacoes' && (
          <Card>
            <CardContent className="p-6">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-[var(--info-light)] rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-[var(--info)]" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h6v-2H4v2zM4 15h8v-2H4v2zM4 11h10V9H4v2zM4 7h12V5H4v2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-[var(--text-primary)] mb-2">
                  Configurações de Notificações
                </h3>
                <p className="text-[var(--text-secondary)]">
                  Esta seção estará disponível em breve
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}

export default function ConfiguracoesPage() {
  return (
    <ProtectedRoute requiredRole="Proprietario">
      <ConfiguracoesProprietario />
    </ProtectedRoute>
  );
}
